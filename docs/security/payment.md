# Payment & Booking Security Guidelines

## Overview
Security considerations for virtual currency system, booking orders, and high-concurrency operations.

## Virtual Currency Security

### Wallet Protection
- All wallet operations must be authenticated
- Use optimistic locking to prevent race conditions
- Validate all amounts (positive, reasonable limits)
- Log all wallet transactions for audit trail

### Transaction Integrity
- Atomic operations for balance updates
- Database transactions for multi-table operations
- Rollback mechanisms for failed operations
- Duplicate transaction prevention

### Balance Validation
```python
# Example validation logic
def validate_deposit(amount):
    if amount <= 0:
        raise ValueError("Amount must be positive")
    if amount > 10000.00:
        raise ValueError("Maximum deposit is 10,000.00")
    if amount != round(amount, 2):
        raise ValueError("Amount must have max 2 decimal places")
```

## Booking Security

### Order Creation Security
- Validate user owns the rider being booked for
- Verify schedule exists and is active for travel date
- Check travel date is not in the past
- Prevent duplicate orders for same user/schedule/date

### Seat Assignment Security
- Use database unique constraints to prevent double-booking
- Atomic seat lock creation with order creation
- Immediate cleanup of failed bookings
- Validate seat numbers match train configuration

### Payment Window Security
- Server-side expiry validation (never trust client)
- Automatic cleanup of expired orders
- Grace period for payment processing delays
- Prevent payment after expiry

## Concurrency Control

### Optimistic Locking Strategy
```python
# Example optimistic locking implementation
def update_wallet_balance(user_id, amount, description):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            wallet = UserWallet.get(user_id=user_id)
            old_version = wallet.version
            
            # Validate sufficient balance for payments
            if amount < 0 and wallet.balance + amount < 0:
                raise InsufficientBalanceError()
            
            # Update balance and version
            wallet.balance += amount
            wallet.version += 1
            
            # Save with version check
            wallet.save(expected_version=old_version)
            
            # Create transaction record
            create_transaction_record(user_id, amount, wallet.balance - amount, wallet.balance, description)
            
            return wallet.balance
            
        except OptimisticLockError:
            if attempt == max_retries - 1:
                raise ConcurrencyError("Failed after maximum retries")
            time.sleep(0.1 * (2 ** attempt))  # Exponential backoff
```

### Database Transaction Management
- Use appropriate isolation levels (READ_COMMITTED minimum)
- Keep transactions short to reduce lock contention
- Implement deadlock detection and retry logic
- Use connection pooling for high concurrency

### Seat Booking Race Conditions
```python
# Example seat booking with race condition handling
def book_seat(schedule_id, travel_date, seat_preference, user_id, rider_id):
    try:
        # Start database transaction
        with database.transaction():
            # Find available seat
            seat_number = find_available_seat(schedule_id, travel_date, seat_preference)
            if not seat_number:
                raise NoSeatsAvailableError()
            
            # Create seat lock (unique constraint prevents double-booking)
            seat_lock = SeatLock.create(
                schedule_id=schedule_id,
                travel_date=travel_date,
                seat_number=seat_number,
                expires_at=datetime.now() + timedelta(minutes=10)
            )
            
            # Create order
            order = Order.create(
                user_id=user_id,
                rider_id=rider_id,
                schedule_id=schedule_id,
                travel_date=travel_date,
                seat_number=seat_number,
                seat_preference=seat_preference,
                status='PENDING',
                expires_at=seat_lock.expires_at
            )
            
            # Link seat lock to order
            seat_lock.order_id = order.id
            seat_lock.save()
            
            return order
            
    except UniqueConstraintError:
        # Seat was booked by another user, try again with different seat
        raise SeatUnavailableError("Selected seat no longer available")
```

## Rate Limiting

### API Rate Limits
- Wallet deposits: 10 requests per minute per user
- Order creation: 5 requests per minute per user
- Payment attempts: 3 requests per minute per order
- General API: 100 requests per minute per user

### Abuse Prevention
- Monitor for unusual booking patterns
- Limit concurrent pending orders per user (max 3)
- Prevent rapid order creation/cancellation cycles
- Log suspicious activities

## Input Validation

### Amount Validation
```python
def validate_currency_amount(amount):
    # Check type
    if not isinstance(amount, (int, float, Decimal)):
        raise ValidationError("Amount must be numeric")
    
    # Convert to Decimal for precision
    amount = Decimal(str(amount))
    
    # Check precision
    if amount.as_tuple().exponent < -2:
        raise ValidationError("Amount cannot have more than 2 decimal places")
    
    # Check range
    if amount <= 0:
        raise ValidationError("Amount must be positive")
    
    if amount > Decimal('999999.99'):
        raise ValidationError("Amount exceeds maximum limit")
    
    return amount
```

### Order Validation
- Validate all foreign key references exist
- Check user permissions for rider access
- Verify schedule is active and available
- Validate travel date format and range

## Logging and Monitoring

### Security Events to Log
```json
{
    "timestamp": "2024-02-15T10:30:00Z",
    "event_type": "WALLET_DEPOSIT",
    "user_id": 123,
    "amount": 500.00,
    "balance_after": 1750.50,
    "ip_address": "*************",
    "success": true
}
```

### Critical Events
- All wallet transactions (deposits, payments, refunds)
- Order creation and status changes
- Payment attempts (success/failure)
- Concurrent modification conflicts
- Expired order cleanups
- Suspicious booking patterns

### Monitoring Alerts
- High rate of payment failures
- Unusual wallet deposit patterns
- Excessive optimistic lock conflicts
- Long-running database transactions
- High number of expired orders

## Cleanup Job Security

### Expired Order Cleanup
```python
def secure_cleanup_expired_orders():
    # Use database transaction for consistency
    with database.transaction():
        # Find expired orders
        expired_orders = Order.query.filter(
            status='PENDING',
            expires_at < datetime.now()
        ).with_for_update()  # Lock rows to prevent race conditions
        
        for order in expired_orders:
            # Update order status
            order.status = 'EXPIRED'
            order.save()
            
            # Release seat lock
            SeatLock.query.filter(order_id=order.id).delete()
            
            # Log cleanup action
            log_order_cleanup(order.id, order.user_id, "expired")
```

### Job Monitoring
- Track cleanup job execution times
- Monitor number of orders cleaned up
- Alert on cleanup job failures
- Ensure jobs don't overlap or conflict

## Error Handling

### Secure Error Messages
- Never expose internal system details
- Use generic error messages for security-sensitive operations
- Log detailed errors server-side for debugging
- Implement proper error codes for client handling

### Payment Error Handling
```python
# Example secure error handling
try:
    process_payment(order_id, user_id)
except InsufficientBalanceError:
    return {"error": "INSUFFICIENT_BALANCE", "message": "Insufficient wallet balance"}
except OptimisticLockError:
    return {"error": "CONCURRENT_MODIFICATION", "message": "Please try again"}
except Exception as e:
    log_error(f"Payment processing error: {str(e)}", user_id=user_id, order_id=order_id)
    return {"error": "PAYMENT_ERROR", "message": "Payment processing failed"}
```

## Implementation Checklist

### Wallet Security
- [ ] Implement optimistic locking for balance updates
- [ ] Validate all deposit amounts and limits
- [ ] Log all wallet transactions
- [ ] Implement rate limiting for deposits
- [ ] Add balance validation before payments

### Booking Security
- [ ] Use unique constraints for seat booking
- [ ] Implement atomic order creation with seat locks
- [ ] Validate user permissions for rider booking
- [ ] Add expiry validation for all payment attempts
- [ ] Implement cleanup jobs with proper locking

### Concurrency Security
- [ ] Use appropriate database isolation levels
- [ ] Implement retry logic with exponential backoff
- [ ] Add deadlock detection and handling
- [ ] Monitor and alert on high conflict rates
- [ ] Test under high concurrency scenarios
