# Payment & Booking Database Schema

## Overview
Virtual currency system with booking orders, payment processing, and concurrency handling for seat reservations.

## Models

### UserWallet Model
```python
class UserWallet:
    id: BigInteger (Primary Key, Auto Increment)
    user_id: BigInteger (Foreign Key -> User.id, Unique, Cascade Delete)
    balance: Decimal(10,2) (Required, Default: 1000.00)  # Virtual currency
    created_at: DateTime (Auto-generated)
    updated_at: DateTime (Auto-updated)
    version: Integer (Default: 0)  # For optimistic locking
```

### WalletTransaction Model
```python
class WalletTransaction:
    id: BigInteger (Primary Key, Auto Increment)
    user_id: BigInteger (Foreign Key -> User.id, Cascade Delete)
    transaction_type: Enum (Required)  # DEPOSIT, PAYMENT, REFUND
    amount: Decimal(10,2) (Required)
    balance_before: Decimal(10,2) (Required)
    balance_after: Decimal(10,2) (Required)
    reference_id: String(100) (Nullable)  # Order ID for payments/refunds
    description: String(255) (Required)
    created_at: DateTime (Auto-generated)
```

### Order Model
```python
class Order:
    id: BigInteger (Primary Key, Auto Increment)
    order_number: String(20) (Unique, Required)  # e.g., "ORD20240215001"
    user_id: BigInteger (Foreign Key -> User.id, Cascade Delete)
    rider_id: BigInteger (Foreign Key -> Rider.id)
    schedule_id: BigInteger (Foreign Key -> Schedule.id)
    travel_date: Date (Required)
    seat_number: String(10) (Required)  # e.g., "05C"
    seat_preference: String(1) (Nullable)  # User's original preference A-E
    price: Decimal(10,2) (Required)
    status: Enum (Required)  # PENDING, PAID, CANCELLED, EXPIRED
    expires_at: DateTime (Required)  # 10 minutes from creation
    paid_at: DateTime (Nullable)
    created_at: DateTime (Auto-generated)
    updated_at: DateTime (Auto-updated)
    version: Integer (Default: 0)  # For optimistic locking
```

### SeatLock Model
```python
class SeatLock:
    id: BigInteger (Primary Key, Auto Increment)
    schedule_id: BigInteger (Foreign Key -> Schedule.id)
    travel_date: Date (Required)
    seat_number: String(10) (Required)
    order_id: BigInteger (Foreign Key -> Order.id, Cascade Delete)
    locked_at: DateTime (Auto-generated)
    expires_at: DateTime (Required)  # Same as order expiry
```

## Indexes
Required indexes for performance and concurrency:
- `user_wallets.user_id` (unique constraint)
- `user_wallets.version` (for optimistic locking)
- `wallet_transactions.user_id + created_at` (composite)
- `wallet_transactions.reference_id`
- `orders.order_number` (unique constraint)
- `orders.user_id + status` (composite)
- `orders.status + expires_at` (composite, for cleanup jobs)
- `orders.version` (for optimistic locking)
- `seat_locks.schedule_id + travel_date + seat_number` (unique composite)
- `seat_locks.expires_at` (for cleanup jobs)

## Enums

### TransactionType
```python
DEPOSIT = "DEPOSIT"      # Adding money to wallet
PAYMENT = "PAYMENT"      # Paying for order
REFUND = "REFUND"        # Refund from cancelled order
```

### OrderStatus
```python
PENDING = "PENDING"      # Order created, payment pending
PAID = "PAID"           # Payment successful, ticket confirmed
CANCELLED = "CANCELLED"  # User cancelled before payment
EXPIRED = "EXPIRED"      # Payment window expired
```

## Data Constraints

### Wallet Rules
- Initial balance: 1000.00 virtual currency units
- Balance cannot go negative
- All amounts must have 2 decimal places
- Version field incremented on every balance change

### Order Rules
- Order number format: "ORD" + YYYYMMDD + 3-digit sequence
- Expires exactly 10 minutes after creation
- Price must match route base price at time of booking
- Seat number format: 2-digit row + 1 letter (e.g., "05C")

### Seat Lock Rules
- One lock per seat per travel date
- Lock expires with order expiry
- Automatic cleanup when order is paid/cancelled/expired

## Sample Data

### User Wallets
```python
user_wallets = [
    {
        "user_id": 1,
        "balance": 1000.00,
        "version": 0
    },
    {
        "user_id": 2, 
        "balance": 850.50,
        "version": 3
    }
]
```

### Orders
```python
orders = [
    {
        "order_number": "ORD20240215001",
        "user_id": 1,
        "rider_id": 1,
        "schedule_id": 1,
        "travel_date": "2024-02-20",
        "seat_number": "05E",
        "seat_preference": "E",
        "price": 89.99,
        "status": "PENDING",
        "expires_at": "2024-02-15T10:40:00Z",
        "version": 0
    }
]
```

### Wallet Transactions
```python
wallet_transactions = [
    {
        "user_id": 1,
        "transaction_type": "DEPOSIT",
        "amount": 500.00,
        "balance_before": 1000.00,
        "balance_after": 1500.00,
        "reference_id": None,
        "description": "Manual deposit"
    },
    {
        "user_id": 1,
        "transaction_type": "PAYMENT", 
        "amount": -89.99,
        "balance_before": 1500.00,
        "balance_after": 1410.01,
        "reference_id": "ORD20240215001",
        "description": "Train ticket payment - NYC to Boston"
    }
]
```

## Concurrency Handling

### Optimistic Locking Strategy
- Use version fields on UserWallet and Order models
- Increment version on every update
- Retry failed operations with exponential backoff
- Maximum 3 retry attempts before failure

### Seat Reservation Process
1. **Check Availability**: Query available seats for date/schedule
2. **Create Seat Lock**: Insert into seat_locks table (unique constraint prevents double-booking)
3. **Create Order**: Generate order with PENDING status
4. **Set Expiry**: 10-minute timer starts
5. **Return to User**: Order created, payment window active

### Database Transaction Isolation
- Use READ_COMMITTED isolation level minimum
- Critical operations in database transactions
- Deadlock detection and retry logic

## Cleanup Jobs

### Expired Order Cleanup (Every Minute)
```python
# Pseudocode for cleanup job
def cleanup_expired_orders():
    expired_orders = Order.query.filter(
        status='PENDING',
        expires_at < current_time
    )
    
    for order in expired_orders:
        # Update order status
        order.status = 'EXPIRED'
        order.save()
        
        # Release seat lock
        SeatLock.query.filter(order_id=order.id).delete()
        
        # Send notification (optional)
        notify_order_expired(order.user_id, order.order_number)
```

### Orphaned Seat Lock Cleanup (Every 5 Minutes)
```python
def cleanup_orphaned_locks():
    # Remove locks without corresponding pending orders
    orphaned_locks = SeatLock.query.filter(
        expires_at < current_time
    )
    orphaned_locks.delete()
```

## Business Logic Notes

### Order Number Generation
- Format: ORD + YYYYMMDD + sequential number
- Reset sequence daily
- Thread-safe generation using database sequences

### Seat Assignment Algorithm
1. User selects preferred seat letter (A, B, C, D, E)
2. Query available seats with that letter, ordered by row number
3. If preferred letter available, assign first available
4. If no preferred letter, assign random available seat
5. Create seat lock immediately upon assignment

### Payment Window Management
- 10-minute countdown starts when order is created
- Server-Sent Events (SSE) notify frontend of time remaining
- Automatic cleanup releases seats for other users
- Grace period of 30 seconds for payment processing
