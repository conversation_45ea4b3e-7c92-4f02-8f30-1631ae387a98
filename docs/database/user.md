# User Database Schema

## Overview
Database schema for user management with simplified registration using username and password.

## Models

### User Model
```python
class User:
    id: <PERSON><PERSON><PERSON><PERSON> (Primary Key, Auto Increment)
    username: String(50) (Unique, Required)
    password_hash: String(255) (Required)
    created_at: DateTime (Auto-generated)
    updated_at: DateTime (Auto-updated)
    last_login_at: DateTime (Nullable)
    is_active: <PERSON><PERSON><PERSON> (Default: True)
```

### UserSession Model
```python
class UserSession:
    id: BigInteger (Primary Key, Auto Increment)
    user_id: BigInteger (Foreign Key -> User.id, Cascade Delete)
    session_token: String(255) (Unique, Required)
    expires_at: DateTime (Required)
    created_at: DateTime (Auto-generated)
    ip_address: String(45) (Nullable)
    user_agent: Text (Nullable)
    is_active: Boolean (Default: True)
```

## Indexes
Required indexes for performance:
- `users.username` (unique constraint)
- `users.is_active`
- `user_sessions.session_token` (unique constraint)
- `user_sessions.user_id + is_active` (composite)
- `user_sessions.expires_at`

## Data Constraints

### Username Rules
- 3-50 characters
- Alphanumeric characters and underscores only
- Case-insensitive (stored in lowercase)
- Must be unique

### Password Rules
- Minimum 6 characters
- No maximum length (will be hashed)
- Stored as bcrypt hash with salt rounds >= 12

## Sample Data
```python
# Example user records
users = [
    {
        "username": "john_doe",
        "password_hash": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PJ/..e"
    },
    {
        "username": "alice_smith",
        "password_hash": "$2b$12$8k1p3ouCSOhHO4IUHUBqaOxvuK1aVBanHNpBkeUBiAMqd7l1o9K5W"
    }
]
```

## Cleanup Jobs
- Remove expired sessions every hour
- Archive inactive users after 1 year (optional)
