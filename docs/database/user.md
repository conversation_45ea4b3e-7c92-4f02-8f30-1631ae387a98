# User Database Schema

## Overview
Database schema for user management with simplified registration using username and password.

## Tables

### Users Table
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Indexes
```sql
-- Performance indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_user_active ON user_sessions(user_id, is_active);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
```

## Data Constraints

### Username Rules
- 3-50 characters
- Alphanumeric characters and underscores only
- Case-insensitive (stored in lowercase)
- Must be unique

### Password Rules
- Minimum 6 characters
- No maximum length (will be hashed)
- Stored as bcrypt hash with salt rounds >= 12

## Sample Data
```sql
-- Example user records
INSERT INTO users (username, password_hash) VALUES 
('john_doe', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PJ/..e'),
('alice_smith', '$2b$12$8k1p3ouCSOhHO4IUHUBqaOxvuK1aVBanHNpBkeUBiAMqd7l1o9K5W');
```

## Cleanup Jobs
- Remove expired sessions every hour
- Archive inactive users after 1 year (optional)
