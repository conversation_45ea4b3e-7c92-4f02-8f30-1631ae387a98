# Train Schedule Database Schema

## Overview
Database models for train schedules, routes, and seat management with single operator (Nomad Railways).

## Models

### Route Model
```python
class Route:
    id: BigInteger (Primary Key, Auto Increment)
    origin_city: String(100) (Required)
    destination_city: String(100) (Required)
    distance_km: Integer (Required)
    base_price: Decimal(10,2) (Required)  # Fixed price for this route
    is_active: <PERSON><PERSON><PERSON> (Default: True)
    created_at: DateTime (Auto-generated)
```

### Train Model
```python
class Train:
    id: BigInteger (Primary Key, Auto Increment)
    train_number: String(20) (Unique, Required)  # e.g., "NR001"
    train_name: String(100) (Required)  # e.g., "City Express"
    total_seats: Integer (Required)  # e.g., 200
    seat_configuration: String(10) (Required)  # e.g., "ABCDE" (5 seats per row)
    is_active: Boolean (Default: True)
    created_at: DateTime (Auto-generated)
```

### Schedule Model
```python
class Schedule:
    id: BigInteger (Primary Key, Auto Increment)
    train_id: BigInteger (Foreign Key -> Train.id)
    route_id: BigInteger (Foreign Key -> Route.id)
    departure_time: Time (Required)  # Daily departure time, e.g., "09:00"
    arrival_time: Time (Required)    # Daily arrival time, e.g., "13:30"
    days_of_week: String(7) (Required)  # "1111100" (Mon-Fri), "1111111" (Daily)
    is_active: Boolean (Default: True)
    created_at: DateTime (Auto-generated)
```

### Rider Model
```python
class Rider:
    id: BigInteger (Primary Key, Auto Increment)
    user_id: BigInteger (Foreign Key -> User.id, Cascade Delete)
    first_name: String(100) (Required)
    last_name: String(100) (Required)
    date_of_birth: Date (Required)
    phone: String(20) (Nullable)
    is_primary: Boolean (Default: False)  # True for the account owner
    created_at: DateTime (Auto-generated)
    updated_at: DateTime (Auto-updated)
```

## Indexes
Required indexes for performance:
- `routes.origin_city + destination_city` (composite)
- `routes.is_active`
- `trains.train_number` (unique constraint)
- `trains.is_active`
- `schedules.train_id + route_id` (composite)
- `schedules.is_active`
- `riders.user_id`

## Data Constraints

### Route Rules
- Origin and destination cities must be different
- Distance must be positive
- Base price must be positive
- City names are case-insensitive (stored in title case)

### Train Rules
- Train number format: "NR" + 3 digits (e.g., "NR001", "NR150")
- Total seats must be positive and divisible by seat configuration length
- Seat configuration uses letters A-E for 5-seat rows, A-F for 6-seat rows, etc.

### Schedule Rules
- Departure time must be before arrival time
- Days of week: 7-character string where 1=active, 0=inactive
- Position represents: Mon(0), Tue(1), Wed(2), Thu(3), Fri(4), Sat(5), Sun(6)

### Rider Rules
- Each user must have exactly one primary rider (themselves)
- Date of birth must be in the past
- Phone format validation (optional field)

## Sample Data

### Routes
```python
routes = [
    {
        "origin_city": "New York",
        "destination_city": "Boston",
        "distance_km": 350,
        "base_price": 89.99
    },
    {
        "origin_city": "Boston",
        "destination_city": "New York", 
        "distance_km": 350,
        "base_price": 89.99
    },
    {
        "origin_city": "Los Angeles",
        "destination_city": "San Francisco",
        "distance_km": 615,
        "base_price": 129.99
    }
]
```

### Trains
```python
trains = [
    {
        "train_number": "NR001",
        "train_name": "Northeast Express",
        "total_seats": 200,
        "seat_configuration": "ABCDE"  # 40 rows × 5 seats = 200 seats
    },
    {
        "train_number": "NR002", 
        "train_name": "Coast Runner",
        "total_seats": 180,
        "seat_configuration": "ABCDE"  # 36 rows × 5 seats = 180 seats
    }
]
```

### Schedules
```python
schedules = [
    {
        "train_id": 1,  # NR001
        "route_id": 1,  # NYC -> Boston
        "departure_time": "09:00",
        "arrival_time": "13:30",
        "days_of_week": "1111111"  # Daily
    },
    {
        "train_id": 1,  # NR001
        "route_id": 2,  # Boston -> NYC
        "departure_time": "15:00", 
        "arrival_time": "19:30",
        "days_of_week": "1111111"  # Daily
    }
]
```

## Business Logic Notes

### Seat Numbering Convention
- Seats numbered as: `{row_number}{seat_letter}`
- Examples: 01A, 01B, 01C, 01D, 01E, 02A, 02B, etc.
- Row numbers are zero-padded (01, 02, ..., 10, 11, etc.)

### Seat Preference Logic
- User selects preferred seat letter (A, B, C, D, E)
- System searches for available seats with that letter across all rows
- If preferred letter unavailable, assign random available seat
- Seat assignment happens during booking process (not in schedule module)
