# User Management API

## Overview
REST API endpoints for user registration, authentication, and profile management.

## Authentication
All protected endpoints require a session token in the Authorization header:
```
Authorization: Bearer {session_token}
```

## Endpoints

### 1. User Registration
**POST** `/api/auth/register`

**Request Body:**
```json
{
    "username": "john_doe",
    "password": "mypassword123"
}
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "User registered successfully",
    "data": {
        "user_id": 123,
        "username": "john_doe"
    }
}
```

**Error Responses:**
- 400: Validation errors (username format, password length, required fields)
- 409: Username already exists

### 2. User Login
**POST** `/api/auth/login`

**Request Body:**
```json
{
    "username": "john_doe",
    "password": "mypassword123"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 123,
            "username": "john_doe"
        },
        "session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_at": "2024-01-15T10:30:00Z"
    }
}
```

**Error Responses:**
- 401: Invalid credentials
- 403: Account not active
- 429: Too many login attempts

### 3. User Logout
**POST** `/api/auth/logout`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

### 4. Get User Profile
**GET** `/api/users/profile`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "username": "john_doe",
        "created_at": "2024-01-01T10:00:00Z",
        "last_login_at": "2024-01-15T09:00:00Z"
    }
}
```

### 5. Change Password
**PUT** `/api/auth/change-password`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Request Body:**
```json
{
    "current_password": "oldpassword123",
    "new_password": "newpassword456"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Password changed successfully"
}
```

**Error Responses:**
- 400: Invalid current password
- 422: New password doesn't meet requirements

## Validation Rules

### Username Validation
- 3-50 characters
- Alphanumeric characters and underscores only
- Case-insensitive
- Required field

### Password Validation
- Minimum 6 characters
- Required field

## Error Response Format

All error responses follow this format:
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "username": ["Username is required"],
            "password": ["Password must be at least 6 characters"]
        }
    }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_FAILED`: Invalid credentials
- `AUTHORIZATION_REQUIRED`: Missing or invalid session token
- `ACCOUNT_NOT_FOUND`: User account doesn't exist
- `ACCOUNT_INACTIVE`: User account is deactivated
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `TOKEN_EXPIRED`: Session token expired
- `TOKEN_INVALID`: Invalid token format or value
- `USERNAME_EXISTS`: Username already taken

## Rate Limiting
- Login attempts: 5 attempts per IP per 15 minutes
- Registration: 3 attempts per IP per hour
- Password change: 5 attempts per user per hour
