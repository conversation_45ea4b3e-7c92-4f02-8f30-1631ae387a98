# Payment & Booking API

## Overview
Virtual currency system with booking orders, payment processing, and real-time notifications.

## Authentication
All endpoints require session token:
```
Authorization: Bearer {session_token}
```

## Endpoints

### 1. Get Wallet Balance
**GET** `/api/wallet/balance`

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "balance": 1250.50,
        "currency": "VCC"  // Virtual Currency Credits
    }
}
```

### 2. Add Money to Wallet
**POST** `/api/wallet/deposit`

**Request Body:**
```json
{
    "amount": 500.00,
    "description": "Manual deposit"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Deposit successful",
    "data": {
        "transaction_id": 123,
        "amount": 500.00,
        "balance_before": 1250.50,
        "balance_after": 1750.50,
        "description": "Manual deposit"
    }
}
```

**Error Responses:**
- 400: Invalid amount (must be positive, max 10000.00 per transaction)

### 3. Get Transaction History
**GET** `/api/wallet/transactions`

**Query Parameters:**
```
page: integer (optional, default: 1)
limit: integer (optional, default: 20, max: 100)
type: string (optional, filter by DEPOSIT/PAYMENT/REFUND)
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "transactions": [
            {
                "id": 123,
                "transaction_type": "DEPOSIT",
                "amount": 500.00,
                "balance_before": 1250.50,
                "balance_after": 1750.50,
                "reference_id": null,
                "description": "Manual deposit",
                "created_at": "2024-02-15T10:30:00Z"
            },
            {
                "id": 124,
                "transaction_type": "PAYMENT",
                "amount": -89.99,
                "balance_before": 1750.50,
                "balance_after": 1660.51,
                "reference_id": "ORD20240215001",
                "description": "Train ticket payment - New York to Boston",
                "created_at": "2024-02-15T11:15:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 3,
            "total_items": 45,
            "items_per_page": 20
        }
    }
}
```

### 4. Create Booking Order
**POST** `/api/bookings/create`

**Request Body:**
```json
{
    "schedule_id": 1,
    "travel_date": "2024-02-20",
    "rider_id": 1,
    "seat_preference": "E"  // Optional: A, B, C, D, E
}
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "Order created successfully",
    "data": {
        "order_number": "ORD20240215001",
        "schedule": {
            "train_number": "NR001",
            "train_name": "Northeast Express",
            "origin_city": "New York",
            "destination_city": "Boston",
            "departure_time": "09:00",
            "arrival_time": "13:30"
        },
        "travel_date": "2024-02-20",
        "rider": {
            "first_name": "John",
            "last_name": "Doe"
        },
        "seat_number": "05E",
        "seat_preference": "E",
        "price": 89.99,
        "status": "PENDING",
        "expires_at": "2024-02-15T10:40:00Z",
        "time_remaining_seconds": 600
    }
}
```

**Error Responses:**
- 400: Invalid schedule, date, or rider
- 409: No seats available
- 422: Travel date must be in future

### 5. Get Order Details
**GET** `/api/bookings/orders/{order_number}`

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "order_number": "ORD20240215001",
        "schedule": {
            "train_number": "NR001",
            "train_name": "Northeast Express",
            "origin_city": "New York",
            "destination_city": "Boston",
            "departure_time": "09:00",
            "arrival_time": "13:30"
        },
        "travel_date": "2024-02-20",
        "rider": {
            "first_name": "John",
            "last_name": "Doe"
        },
        "seat_number": "05E",
        "price": 89.99,
        "status": "PENDING",
        "expires_at": "2024-02-15T10:40:00Z",
        "time_remaining_seconds": 420,
        "created_at": "2024-02-15T10:30:00Z"
    }
}
```

### 6. Pay for Order
**POST** `/api/bookings/orders/{order_number}/pay`

**Success Response (200):**
```json
{
    "success": true,
    "message": "Payment successful",
    "data": {
        "order_number": "ORD20240215001",
        "status": "PAID",
        "paid_at": "2024-02-15T10:35:00Z",
        "transaction_id": 125,
        "remaining_balance": 1660.51
    }
}
```

**Error Responses:**
- 400: Order already paid or expired
- 402: Insufficient wallet balance
- 409: Optimistic lock conflict (retry)

### 7. Cancel Order
**POST** `/api/bookings/orders/{order_number}/cancel`

**Success Response (200):**
```json
{
    "success": true,
    "message": "Order cancelled successfully",
    "data": {
        "order_number": "ORD20240215001",
        "status": "CANCELLED",
        "seat_released": "05E"
    }
}
```

**Error Responses:**
- 400: Order already paid or expired
- 404: Order not found

### 8. Get User Orders
**GET** `/api/bookings/orders`

**Query Parameters:**
```
page: integer (optional, default: 1)
limit: integer (optional, default: 20)
status: string (optional, filter by PENDING/PAID/CANCELLED/EXPIRED)
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "orders": [
            {
                "order_number": "ORD20240215001",
                "train_number": "NR001",
                "route": "New York → Boston",
                "travel_date": "2024-02-20",
                "departure_time": "09:00",
                "seat_number": "05E",
                "rider_name": "John Doe",
                "price": 89.99,
                "status": "PAID",
                "created_at": "2024-02-15T10:30:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 2,
            "total_items": 25,
            "items_per_page": 20
        }
    }
}
```

## Server-Sent Events (SSE)

### 9. Order Status Updates
**GET** `/api/bookings/orders/{order_number}/events`

**Headers:**
```
Accept: text/event-stream
Cache-Control: no-cache
Authorization: Bearer {session_token}
```

**Event Stream Format:**
```
event: order_created
data: {"order_number": "ORD20240215001", "expires_at": "2024-02-15T10:40:00Z", "time_remaining": 600}

event: time_update
data: {"order_number": "ORD20240215001", "time_remaining": 540}

event: payment_success
data: {"order_number": "ORD20240215001", "status": "PAID", "paid_at": "2024-02-15T10:35:00Z"}

event: order_expired
data: {"order_number": "ORD20240215001", "status": "EXPIRED", "seat_released": "05E"}
```

## Validation Rules

### Deposit Validation
- Amount: Required, positive number, 2 decimal places
- Minimum deposit: 1.00
- Maximum deposit: 10,000.00 per transaction
- Maximum daily deposits: 50,000.00

### Booking Validation
- Schedule ID: Must exist and be active
- Travel date: Must be today or future date
- Rider ID: Must belong to authenticated user
- Seat preference: Optional, must be valid letter (A-E)

## Concurrency Handling

### Optimistic Locking Implementation
```json
// Example retry response for wallet operations
{
    "success": false,
    "error": {
        "code": "OPTIMISTIC_LOCK_CONFLICT",
        "message": "Operation failed due to concurrent modification",
        "retry_after_ms": 100
    }
}
```

### Seat Booking Race Conditions
- Database unique constraints prevent double-booking
- Failed seat locks return specific error codes
- Automatic retry with different seat assignment

### High-Concurrency Strategies
1. **Database Connection Pooling**: Handle multiple simultaneous requests
2. **Optimistic Locking**: Prevent wallet balance conflicts
3. **Unique Constraints**: Prevent seat double-booking
4. **Exponential Backoff**: Retry failed operations
5. **Circuit Breaker**: Prevent cascade failures

## Error Codes
- `INSUFFICIENT_BALANCE`: Wallet balance too low for payment
- `ORDER_EXPIRED`: Payment window has closed
- `ORDER_ALREADY_PAID`: Cannot modify paid order
- `SEAT_UNAVAILABLE`: Requested seat already booked
- `OPTIMISTIC_LOCK_CONFLICT`: Concurrent modification detected
- `PAYMENT_PROCESSING_ERROR`: Payment failed during processing
- `ORDER_NOT_FOUND`: Order doesn't exist or doesn't belong to user

## Business Logic

### Payment Window Management
- 10-minute countdown from order creation
- SSE updates every 30 seconds with remaining time
- 30-second grace period for payment processing
- Automatic cleanup releases seats immediately

### Seat Assignment Priority
1. Try user's preferred seat letter across all rows (ascending order)
2. If no preferred seats available, assign random available seat
3. Prefer lower row numbers when multiple options exist
4. Ensure seat assignment is atomic with order creation
