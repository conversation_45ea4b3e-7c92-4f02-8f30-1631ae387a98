# Train Schedule API

## Overview
REST API endpoints for train schedule search, route information, and rider management.

## Authentication
Protected endpoints require session token:
```
Authorization: Bearer {session_token}
```

## Endpoints

### 1. Search Train Schedules
**GET** `/api/trains/search`

**Query Parameters:**
```
origin_city: string (required) - Departure city
destination_city: string (required) - Arrival city  
travel_date: string (required) - Date in YYYY-MM-DD format
```

**Example Request:**
```
GET /api/trains/search?origin_city=New York&destination_city=Boston&travel_date=2024-02-15
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "search_criteria": {
            "origin_city": "New York",
            "destination_city": "Boston",
            "travel_date": "2024-02-15"
        },
        "trains": [
            {
                "schedule_id": 1,
                "train_number": "NR001",
                "train_name": "Northeast Express",
                "departure_time": "09:00",
                "arrival_time": "13:30",
                "duration_minutes": 270,
                "price": 89.99,
                "available_seats": 156,
                "total_seats": 200
            },
            {
                "schedule_id": 3,
                "train_number": "NR003", 
                "train_name": "Morning Commuter",
                "departure_time": "07:30",
                "arrival_time": "12:00",
                "duration_minutes": 270,
                "price": 89.99,
                "available_seats": 89,
                "total_seats": 150
            }
        ]
    }
}
```

**Error Responses:**
- 400: Invalid date format or missing parameters
- 404: No routes found between cities

### 2. Get Available Routes
**GET** `/api/trains/routes`

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "routes": [
            {
                "id": 1,
                "origin_city": "New York",
                "destination_city": "Boston",
                "distance_km": 350,
                "base_price": 89.99
            },
            {
                "id": 2,
                "origin_city": "Boston", 
                "destination_city": "New York",
                "distance_km": 350,
                "base_price": 89.99
            },
            {
                "id": 3,
                "origin_city": "Los Angeles",
                "destination_city": "San Francisco", 
                "distance_km": 615,
                "base_price": 129.99
            }
        ]
    }
}
```

### 3. Get Train Details
**GET** `/api/trains/{train_id}`

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "train": {
            "id": 1,
            "train_number": "NR001",
            "train_name": "Northeast Express",
            "total_seats": 200,
            "seat_configuration": "ABCDE",
            "schedules": [
                {
                    "schedule_id": 1,
                    "route": {
                        "origin_city": "New York",
                        "destination_city": "Boston"
                    },
                    "departure_time": "09:00",
                    "arrival_time": "13:30",
                    "days_of_week": "1111111"
                }
            ]
        }
    }
}
```

### 4. Add Rider (Protected)
**POST** `/api/users/riders`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Request Body:**
```json
{
    "first_name": "Jane",
    "last_name": "Smith", 
    "date_of_birth": "1985-03-20",
    "phone": "+1234567890"
}
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "Rider added successfully",
    "data": {
        "rider_id": 123,
        "first_name": "Jane",
        "last_name": "Smith",
        "date_of_birth": "1985-03-20",
        "phone": "+1234567890",
        "is_primary": false
    }
}
```

### 5. Get User Riders (Protected)
**GET** `/api/users/riders`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "riders": [
            {
                "id": 122,
                "first_name": "John",
                "last_name": "Doe",
                "date_of_birth": "1990-01-15",
                "phone": "+1987654321",
                "is_primary": true
            },
            {
                "id": 123,
                "first_name": "Jane", 
                "last_name": "Smith",
                "date_of_birth": "1985-03-20",
                "phone": "+1234567890",
                "is_primary": false
            }
        ]
    }
}
```

### 6. Update Rider (Protected)
**PUT** `/api/users/riders/{rider_id}`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Request Body:**
```json
{
    "first_name": "Jane",
    "last_name": "Johnson",
    "phone": "+1555666777"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Rider updated successfully",
    "data": {
        "id": 123,
        "first_name": "Jane",
        "last_name": "Johnson", 
        "date_of_birth": "1985-03-20",
        "phone": "+1555666777",
        "is_primary": false
    }
}
```

### 7. Delete Rider (Protected)
**DELETE** `/api/users/riders/{rider_id}`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Rider deleted successfully"
}
```

**Error Response:**
- 403: Cannot delete primary rider

## Validation Rules

### Search Parameters
- `origin_city` and `destination_city`: Required, 2-100 characters
- `travel_date`: Required, must be today or future date, YYYY-MM-DD format
- Cities are case-insensitive

### Rider Validation
- `first_name`, `last_name`: Required, 1-100 characters, letters and spaces only
- `date_of_birth`: Required, must be in the past, YYYY-MM-DD format
- `phone`: Optional, international format preferred

## Business Logic

### Schedule Search Logic
1. Find active route between origin and destination cities
2. Get all active schedules for that route
3. Filter schedules by day of week for travel date
4. Calculate available seats (total seats - booked seats for that date)
5. Return schedules with availability > 0

### Seat Availability Calculation
- Query booking system for confirmed bookings on specific date
- Subtract booked seats from train's total capacity
- Real-time availability (not cached)

### Rider Management Rules
- Users automatically get a primary rider (themselves) on registration
- Primary rider cannot be deleted
- Users can add up to 10 additional riders
- Rider information used during booking process

## Error Codes
- `ROUTE_NOT_FOUND`: No route exists between specified cities
- `INVALID_DATE`: Date format invalid or in the past
- `TRAIN_NOT_FOUND`: Train ID doesn't exist
- `RIDER_LIMIT_EXCEEDED`: Maximum 10 riders per user
- `PRIMARY_RIDER_DELETE`: Cannot delete primary rider
- `RIDER_NOT_FOUND`: Rider doesn't belong to user
