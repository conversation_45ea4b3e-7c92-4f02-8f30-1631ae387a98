# User Management System

## Overview

The user management system handles user registration, authentication, profile management, and session control. This module is the foundation for all user-specific operations in the platform.

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL
);
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Email Verification Table
```sql
CREATE TABLE email_verifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    verification_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_used BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Password Reset Table
```sql
CREATE TABLE password_resets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    reset_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_used BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## API Endpoints

### 1. User Registration
**POST** `/api/auth/register`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+**********",
    "date_of_birth": "1990-01-15"
}
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "User registered successfully. Please check your email for verification.",
    "data": {
        "user_id": 123,
        "email": "<EMAIL>",
        "is_email_verified": false
    }
}
```

**Error Responses:**
- 400: Validation errors (email format, password strength, required fields)
- 409: Email already exists

### 2. User Login
**POST** `/api/auth/login`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 123,
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "is_email_verified": true
        },
        "session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_at": "2024-01-15T10:30:00Z"
    }
}
```

**Error Responses:**
- 401: Invalid credentials
- 403: Account not active or email not verified
- 429: Too many login attempts

### 3. User Logout
**POST** `/api/auth/logout`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

### 4. Get User Profile
**GET** `/api/users/profile`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+**********",
        "date_of_birth": "1990-01-15",
        "is_email_verified": true,
        "created_at": "2024-01-01T10:00:00Z",
        "last_login_at": "2024-01-15T09:00:00Z"
    }
}
```

### 5. Update User Profile
**PUT** `/api/users/profile`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Request Body:**
```json
{
    "first_name": "John",
    "last_name": "Smith",
    "phone": "+**********",
    "date_of_birth": "1990-01-15"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Profile updated successfully",
    "data": {
        "id": 123,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Smith",
        "phone": "+**********",
        "date_of_birth": "1990-01-15"
    }
}
```

### 6. Change Password
**PUT** `/api/auth/change-password`

**Headers:**
```
Authorization: Bearer {session_token}
```

**Request Body:**
```json
{
    "current_password": "OldPassword123!",
    "new_password": "NewSecurePassword456!"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Password changed successfully"
}
```

**Error Responses:**
- 400: Invalid current password
- 422: New password doesn't meet requirements

### 7. Request Password Reset
**POST** `/api/auth/forgot-password`

**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Password reset email sent if account exists"
}
```

### 8. Reset Password
**POST** `/api/auth/reset-password`

**Request Body:**
```json
{
    "reset_token": "abc123def456",
    "new_password": "NewSecurePassword789!"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Password reset successfully"
}
```

**Error Responses:**
- 400: Invalid or expired token
- 422: Password doesn't meet requirements

### 9. Verify Email
**POST** `/api/auth/verify-email`

**Request Body:**
```json
{
    "verification_token": "xyz789abc123"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Email verified successfully"
}
```

### 10. Resend Email Verification
**POST** `/api/auth/resend-verification`

**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Verification email sent if account exists"
}
```

## Validation Rules

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (!@#$%^&*)
- Cannot contain email address or name

### Email Validation
- Valid email format (RFC 5322)
- Maximum 255 characters
- Case-insensitive storage (convert to lowercase)

### Name Validation
- First name: 1-100 characters, letters and spaces only
- Last name: 1-100 characters, letters and spaces only

### Phone Validation
- Optional field
- International format preferred (+country_code)
- 10-20 characters including country code

## Security Considerations

### Session Management
- Session tokens expire after 24 hours of inactivity
- Maximum 5 active sessions per user
- Automatic cleanup of expired sessions
- Session invalidation on password change

### Rate Limiting
- Login attempts: 5 attempts per IP per 15 minutes
- Registration: 3 attempts per IP per hour
- Password reset: 3 requests per email per hour
- Email verification: 5 requests per email per hour

### Password Security
- Use bcrypt with salt rounds >= 12
- Never store plain text passwords
- Implement password history (prevent reuse of last 5 passwords)

### Token Security
- Session tokens: 256-bit random strings
- Verification tokens: 128-bit random strings with 24-hour expiry
- Reset tokens: 128-bit random strings with 1-hour expiry
- All tokens should be cryptographically secure random

## Error Response Format

All error responses follow this format:
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "email": ["Email is required"],
            "password": ["Password must be at least 8 characters"]
        }
    }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_FAILED`: Invalid credentials
- `AUTHORIZATION_REQUIRED`: Missing or invalid session token
- `ACCOUNT_NOT_FOUND`: User account doesn't exist
- `ACCOUNT_INACTIVE`: User account is deactivated
- `EMAIL_NOT_VERIFIED`: Email verification required
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `TOKEN_EXPIRED`: Session or verification token expired
- `TOKEN_INVALID`: Invalid token format or value

## Implementation Notes

### Database Indexes
```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_user_active ON user_sessions(user_id, is_active);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_email_verifications_token ON email_verifications(verification_token);
CREATE INDEX idx_password_resets_token ON password_resets(reset_token);
```

### Cleanup Jobs
Implement scheduled jobs to:
- Remove expired sessions every hour
- Remove expired verification tokens daily
- Remove expired password reset tokens every hour
- Archive inactive users after 2 years (optional)

### Logging Requirements
Log the following events:
- User registration attempts
- Login/logout events
- Password changes
- Failed authentication attempts
- Account lockouts
- Email verification events
